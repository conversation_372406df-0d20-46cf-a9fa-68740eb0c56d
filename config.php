<?php
/**
 * Solomon Islands Scholarship Application - Configuration File
 *
 * This file contains database connection settings and application configuration
 * All settings are in plain text for reliability
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Database Configuration - Direct credentials
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'u787474055_solomonislands');
define('DB_PASSWORD', 'Blackpanther707@707');
define('DB_NAME', 'u787474055_solomonislands');

// Application Configuration
define('APP_NAME', 'Solomon Islands Scholarship Application');
define('APP_VERSION', '1.0.0');
define('ADMIN_EMAIL', '<EMAIL>');

// Security Configuration
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_TIMEOUT', 3600); // 1 hour in seconds

// File Upload Configuration
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('UPLOAD_DIR', 'uploads/');

// Email Configuration
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Solomon Islands Scholarship Program - TEST');

/**
 * Database Connection Class
 */
class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $this->connection = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_NAME);
            
            if ($this->connection->connect_error) {
                throw new Exception("Connection failed: " . $this->connection->connect_error);
            }
            
            // Set charset to UTF-8
            $this->connection->set_charset("utf8mb4");
            
        } catch (Exception $e) {
            error_log("Database connection error: " . $e->getMessage());
            die("Database connection failed. Please try again later.");
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function escape($string) {
        return $this->connection->real_escape_string($string);
    }
    
    public function query($sql) {
        $result = $this->connection->query($sql);
        if (!$result) {
            error_log("SQL Error: " . $this->connection->error . " | Query: " . $sql);
            return false;
        }
        return $result;
    }
    
    public function prepare($sql) {
        return $this->connection->prepare($sql);
    }
    
    public function getLastInsertId() {
        return $this->connection->insert_id;
    }
    
    public function getError() {
        return $this->connection->error;
    }
}

/**
 * Security Functions
 */
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

function validateCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function validatePhone($phone) {
    // Remove all non-digit characters
    $phone = preg_replace('/[^0-9+]/', '', $phone);
    // Check if it's a valid format (basic validation)
    return preg_match('/^[\+]?[0-9]{7,15}$/', $phone);
}

/**
 * Utility Functions
 */
function redirect($url) {
    header("Location: $url");
    exit();
}

function showError($message) {
    $_SESSION['error_message'] = $message;
}

function showSuccess($message) {
    $_SESSION['success_message'] = $message;
}

function getErrorMessage() {
    if (isset($_SESSION['error_message'])) {
        $message = $_SESSION['error_message'];
        unset($_SESSION['error_message']);
        return $message;
    }
    return null;
}

function getSuccessMessage() {
    if (isset($_SESSION['success_message'])) {
        $message = $_SESSION['success_message'];
        unset($_SESSION['success_message']);
        return $message;
    }
    return null;
}

/**
 * Admin Authentication Functions
 */
function isAdminLoggedIn() {
    return isset($_SESSION['admin_user_id']) && isset($_SESSION['admin_username']);
}

function requireAdminLogin() {
    if (!isAdminLoggedIn()) {
        redirect('login.php');
    }
}

function getAdminUserId() {
    return isset($_SESSION['admin_user_id']) ? $_SESSION['admin_user_id'] : null;
}

function getAdminUsername() {
    return isset($_SESSION['admin_username']) ? $_SESSION['admin_username'] : null;
}

/**
 * Logging Function
 */
function logActivity($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    
    $log_entry = "[$timestamp] [$level] [$ip] $message | User Agent: $user_agent" . PHP_EOL;
    
    // Log to file (make sure logs directory exists and is writable)
    if (!file_exists('logs')) {
        mkdir('logs', 0755, true);
    }
    
    file_put_contents('logs/application.log', $log_entry, FILE_APPEND | LOCK_EX);
}

// Error reporting - disabled for production
error_reporting(0);
ini_set('display_errors', 0);

// Set timezone
date_default_timezone_set('Pacific/Guadalcanal');
?>
