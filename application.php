<?php
require_once 'config.php';

// Generate CSRF token
$csrf_token = generateCSRFToken();

// Get any error or success messages
$error_message = getErrorMessage();
$success_message = getSuccessMessage();

// Province options
$provinces = [
    'Central', 'Choiseul', 'Guadalcanal', 'Isabel', 'Makira-Ulawa', 
    'Malaita', 'Rennell and Bellona', 'Temotu', 'Western', 'Honiara'
];

// HEI options
$hei_options = [
    'University of Papua New Guinea (UPNG)', 
    'University of Technology (UOT)', 
    'Papua New Guinea University of Natural Resources and Environment (PNGUNRE)',
    'Divine Word University (DWU)',
    'Pacific Adventist University (PAU)',
    'University of Goroka (UOG)'
];

// PNG Province options
$png_provinces = [
    'National Capital District (NCD)', 'Morobe', 'Western Highlands', 'Eastern Highlands',
    'Madang', 'East New Britain', 'West New Britain', 'Manus', 'New Ireland',
    'Bougainville', 'Gulf', 'Central', 'Milne Bay', 'Oro', 'Southern Highlands',
    'Hela', 'Jiwaka', 'Chimbu', 'Western', 'Enga'
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apply Now - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Page Header -->
    <section class="bg-primary text-white py-5" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="display-5 fw-bold mb-3">
                        <i class="fas fa-graduation-cap me-3"></i>
                        Scholarship Application Form
                    </h1>
                    <p class="lead mb-0">
                        Complete your application for the Papua New Guinea Higher Education Program
                    </p>
                    <div class="mt-3">
                        <span class="badge bg-danger fs-6 px-3 py-2">
                            <i class="fas fa-clock me-2"></i>Applications Open
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="container my-5">
        <!-- Alert Messages -->
        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Application Instructions -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Before You Begin
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="fw-bold text-primary">Required Information:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>Personal details and contact information</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Academic qualifications and transcripts</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Preferred universities and study programs</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Travel and accommodation preferences</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="fw-bold text-primary">Important Notes:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-exclamation text-warning me-2"></i>All fields marked with * are required</li>
                                    <li><i class="fas fa-exclamation text-warning me-2"></i>Ensure all information is accurate</li>
                                    <li><i class="fas fa-exclamation text-warning me-2"></i>You can save and continue later</li>
                                    <li><i class="fas fa-exclamation text-warning me-2"></i>Review before final submission</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Application Form -->
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card shadow">
                    <div class="card-header bg-light">
                        <h2 class="card-title mb-0">
                            <i class="fas fa-file-alt me-2"></i>
                            Scholarship Application Form
                        </h2>
                        <p class="text-muted mb-0 small">Please fill out all required fields marked with *</p>
                    </div>
                    
                    <div class="card-body">
                        <form id="scholarshipForm" action="submit.php" method="POST" novalidate>
                            <!-- CSRF Token -->
                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                            
                            <!-- Progress Bar -->
                            <div class="progress mb-4" style="height: 8px;">
                                <div class="progress-bar" role="progressbar" style="width: 20%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>

                            <!-- Step 1: Basic Information -->
                            <div class="form-step active" data-step="1">
                                <h4 class="text-primary mb-4">
                                    <i class="fas fa-user me-2"></i>
                                    Step 1: Basic Information
                                </h4>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="student_code" class="form-label">
                                            Student Code <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="student_code" name="student_code" 
                                               required maxlength="20" placeholder="Enter your student code">
                                        <div class="invalid-feedback">Please provide a valid student code.</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="selection_status" class="form-label">
                                            Selection Status <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="selection_status" name="selection_status" required>
                                            <option value="">Choose status...</option>
                                            <option value="Selected">Selected</option>
                                            <option value="Pending">Pending</option>
                                            <option value="Under Review">Under Review</option>
                                        </select>
                                        <div class="invalid-feedback">Please select your selection status.</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-8 mb-3">
                                        <label for="full_name" class="form-label">
                                            Full Name <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" 
                                               required maxlength="100" placeholder="Enter your full name">
                                        <div class="invalid-feedback">Please provide your full name.</div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="date_of_birth" class="form-label">Date of Birth</label>
                                        <input type="date" class="form-control" id="date_of_birth" name="date_of_birth">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="gender" class="form-label">Gender</label>
                                        <select class="form-select" id="gender" name="gender">
                                            <option value="">Choose...</option>
                                            <option value="Male">Male</option>
                                            <option value="Female">Female</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               maxlength="100" placeholder="<EMAIL>">
                                        <div class="invalid-feedback">Please provide a valid email address.</div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="phone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               maxlength="20" placeholder="+677 XXXXXXX">
                                        <div class="invalid-feedback">Please provide a valid phone number.</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 2: Location Information -->
                            <div class="form-step" data-step="2">
                                <h4 class="text-primary mb-4">
                                    <i class="fas fa-map-marker-alt me-2"></i>
                                    Step 2: Location Information
                                </h4>

                                <div class="row">
                                    <div class="col-12 mb-4">
                                        <label class="form-label fw-bold">
                                            Province of Origin <span class="text-danger">*</span>
                                        </label>
                                        <div class="row">
                                            <?php foreach($provinces as $province): ?>
                                            <div class="col-md-4 col-6 mb-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox"
                                                           name="province_of_origin[]" value="<?php echo $province; ?>"
                                                           id="origin_<?php echo strtolower(str_replace(' ', '_', $province)); ?>">
                                                    <label class="form-check-label"
                                                           for="origin_<?php echo strtolower(str_replace(' ', '_', $province)); ?>">
                                                        <?php echo $province; ?>
                                                    </label>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <div class="invalid-feedback d-block" id="province_origin_error" style="display: none;">
                                            Please select at least one province of origin.
                                        </div>
                                    </div>

                                    <div class="col-12 mb-3">
                                        <label class="form-label fw-bold">
                                            Current Residential Province
                                        </label>
                                        <div class="row">
                                            <?php foreach($provinces as $province): ?>
                                            <div class="col-md-4 col-6 mb-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox"
                                                           name="current_residential_province[]" value="<?php echo $province; ?>"
                                                           id="current_<?php echo strtolower(str_replace(' ', '_', $province)); ?>">
                                                    <label class="form-check-label"
                                                           for="current_<?php echo strtolower(str_replace(' ', '_', $province)); ?>">
                                                        <?php echo $province; ?>
                                                    </label>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 3: Travel & Institution Information -->
                            <div class="form-step" data-step="3">
                                <h4 class="text-primary mb-4">
                                    <i class="fas fa-plane me-2"></i>
                                    Step 3: Travel & Institution Information
                                </h4>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="mode_of_transport_to_honiara" class="form-label">
                                            Mode of Transport to Honiara <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="mode_of_transport_to_honiara"
                                                name="mode_of_transport_to_honiara" required>
                                            <option value="">Choose...</option>
                                            <option value="Air">Air</option>
                                            <option value="Sea">Sea</option>
                                            <option value="Land">Land</option>
                                            <option value="Other">Other</option>
                                        </select>
                                        <div class="invalid-feedback">Please select a mode of transport.</div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="honiara_pom_route" class="form-label">
                                            Honiara to Port Moresby Route
                                        </label>
                                        <input type="text" class="form-control" id="honiara_pom_route"
                                               name="honiara_pom_route" maxlength="100"
                                               placeholder="e.g., Direct flight, Via Brisbane">
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="hei_name" class="form-label">
                                            Preferred Higher Education Institution <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="hei_name" name="hei_name" required>
                                            <option value="">Choose...</option>
                                            <?php foreach($hei_options as $hei): ?>
                                            <option value="<?php echo $hei; ?>"><?php echo $hei; ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="invalid-feedback">Please select a higher education institution.</div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="png_province" class="form-label">
                                            PNG Province <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="png_province" name="png_province" required>
                                            <option value="">Choose...</option>
                                            <option value="National Capital District">National Capital District</option>
                                            <option value="Morobe">Morobe</option>
                                            <option value="Eastern Highlands">Eastern Highlands</option>
                                            <option value="Western Highlands">Western Highlands</option>
                                            <option value="Central">Central</option>
                                            <option value="Western">Western</option>
                                            <option value="Gulf">Gulf</option>
                                            <option value="Milne Bay">Milne Bay</option>
                                            <option value="Oro">Oro</option>
                                            <option value="Southern Highlands">Southern Highlands</option>
                                            <option value="Enga">Enga</option>
                                            <option value="Simbu">Simbu</option>
                                            <option value="Madang">Madang</option>
                                            <option value="East Sepik">East Sepik</option>
                                            <option value="West Sepik">West Sepik</option>
                                            <option value="Manus">Manus</option>
                                            <option value="New Ireland">New Ireland</option>
                                            <option value="East New Britain">East New Britain</option>
                                            <option value="West New Britain">West New Britain</option>
                                            <option value="Bougainville">Bougainville</option>
                                        </select>
                                        <div class="invalid-feedback">Please select a PNG province.</div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="travel_question" class="form-label">
                                            Travel Experience/Questions
                                        </label>
                                        <textarea class="form-control" id="travel_question" name="travel_question"
                                                  rows="3" maxlength="500"
                                                  placeholder="Any questions or concerns about travel arrangements?"></textarea>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="estimated_cost" class="form-label">
                                            Estimated Travel Cost (SBD)
                                        </label>
                                        <input type="number" class="form-control" id="estimated_cost"
                                               name="estimated_cost" min="0" step="0.01"
                                               placeholder="0.00">
                                    </div>
                                </div>
                            </div>

                            <!-- Step 4: Academic Information -->
                            <div class="form-step" data-step="4">
                                <h4 class="text-primary mb-4">
                                    <i class="fas fa-graduation-cap me-2"></i>
                                    Step 4: Academic Information
                                </h4>

                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <label for="academic_qualifications" class="form-label">
                                            Academic Qualifications <span class="text-danger">*</span>
                                        </label>
                                        <textarea class="form-control" id="academic_qualifications"
                                                  name="academic_qualifications" rows="4" required maxlength="1000"
                                                  placeholder="Please list your academic qualifications, including certificates, diplomas, degrees, and relevant coursework..."></textarea>
                                        <div class="form-text">Include institution names, years of completion, and grades/results where applicable.</div>
                                        <div class="invalid-feedback">Please provide your academic qualifications.</div>
                                    </div>

                                    <div class="col-12 mb-3">
                                        <label for="course_of_study" class="form-label">
                                            Preferred Course of Study <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="course_of_study"
                                               name="course_of_study" required maxlength="200"
                                               placeholder="e.g., Bachelor of Engineering, Master of Business Administration">
                                        <div class="form-text">Specify the exact program or field of study you wish to pursue.</div>
                                        <div class="invalid-feedback">Please specify your preferred course of study.</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 5: Final Details -->
                            <div class="form-step" data-step="5">
                                <h4 class="text-primary mb-4">
                                    <i class="fas fa-file-signature me-2"></i>
                                    Step 5: Final Details
                                </h4>

                                <div class="row">
                                    <div class="col-12 mb-4">
                                        <label for="motivation_letter" class="form-label">
                                            Motivation Letter <span class="text-danger">*</span>
                                        </label>
                                        <textarea class="form-control" id="motivation_letter"
                                                  name="motivation_letter" rows="6" required maxlength="2000"
                                                  placeholder="Please write a detailed motivation letter explaining why you want to study in Papua New Guinea, your career goals, and how this scholarship will help you contribute to Solomon Islands..."></textarea>
                                        <div class="form-text">
                                            <strong>Guidelines:</strong> Explain your motivation for studying in PNG, career aspirations,
                                            how the scholarship aligns with your goals, and your commitment to contributing to Solomon Islands' development.
                                            <br><strong>Word limit:</strong> Approximately 300-400 words.
                                        </div>
                                        <div class="invalid-feedback">Please provide a motivation letter.</div>
                                    </div>

                                    <div class="col-12 mb-4">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6 class="card-title text-primary">
                                                    <i class="fas fa-info-circle me-2"></i>
                                                    Application Review
                                                </h6>
                                                <p class="card-text small mb-2">
                                                    Please review all the information you have provided before submitting your application.
                                                    Once submitted, you will receive a confirmation email with your application reference number.
                                                </p>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="terms_agreement"
                                                           name="terms_agreement" required>
                                                    <label class="form-check-label" for="terms_agreement">
                                                        I confirm that all information provided is accurate and complete.
                                                        I understand that providing false information may result in disqualification. <span class="text-danger">*</span>
                                                    </label>
                                                    <div class="invalid-feedback">You must agree to the terms before submitting.</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Navigation Buttons -->
                            <div class="d-flex justify-content-between mt-4">
                                <button type="button" class="btn btn-secondary" id="prevBtn" style="display: none;">
                                    <i class="fas fa-arrow-left me-2"></i>Previous
                                </button>
                                <button type="button" class="btn btn-primary" id="nextBtn">
                                    Next<i class="fas fa-arrow-right ms-2"></i>
                                </button>
                                <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">
                                    <i class="fas fa-paper-plane me-2"></i>Submit Application
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS - Working Navigation Script -->
    <script src="assets/form-navigation.js"></script>
</body>
</html>
