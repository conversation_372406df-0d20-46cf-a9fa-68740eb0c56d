<?php
/**
 * Admin-specific configuration file
 * This file provides a fallback configuration for admin panel
 * in case the main config.php has issues with .env loading
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Database Configuration - Direct credentials
define('ADMIN_DB_HOST', 'localhost');
define('ADMIN_DB_USERNAME', 'u787474055_solomonislands');
define('ADMIN_DB_PASSWORD', 'Blackpanther707@');
define('ADMIN_DB_NAME', 'u787474055_solomonislands');

// Application Configuration
define('ADMIN_APP_NAME', 'Solomon Islands Scholarship Application');
define('ADMIN_APP_VERSION', '1.0.0');
define('ADMIN_EMAIL', '<EMAIL>');

// Security Configuration
define('ADMIN_CSRF_TOKEN_NAME', 'csrf_token');
define('ADMIN_SESSION_TIMEOUT', 7200); // 2 hours in seconds

/**
 * Admin Database Connection Class
 */
class AdminDatabase {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $this->connection = new mysqli(
                ADMIN_DB_HOST, 
                ADMIN_DB_USERNAME, 
                ADMIN_DB_PASSWORD, 
                ADMIN_DB_NAME
            );
            
            if ($this->connection->connect_error) {
                throw new Exception("Connection failed: " . $this->connection->connect_error);
            }
            
            // Set charset to UTF-8
            $this->connection->set_charset("utf8mb4");
            
        } catch (Exception $e) {
            error_log("Admin database connection error: " . $e->getMessage());
            die("Database connection failed. Please try again later.");
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function escape($string) {
        return $this->connection->real_escape_string($string);
    }
    
    public function query($sql) {
        $result = $this->connection->query($sql);
        if (!$result) {
            error_log("SQL Error: " . $this->connection->error . " | Query: " . $sql);
            return false;
        }
        return $result;
    }
    
    public function prepare($sql) {
        return $this->connection->prepare($sql);
    }
    
    public function getLastInsertId() {
        return $this->connection->insert_id;
    }
    
    public function getError() {
        return $this->connection->error;
    }
}

/**
 * Admin Security Functions
 */
function generateAdminCSRFToken() {
    if (!isset($_SESSION[ADMIN_CSRF_TOKEN_NAME])) {
        $_SESSION[ADMIN_CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[ADMIN_CSRF_TOKEN_NAME];
}

function validateAdminCSRFToken($token) {
    return isset($_SESSION[ADMIN_CSRF_TOKEN_NAME]) && 
           hash_equals($_SESSION[ADMIN_CSRF_TOKEN_NAME], $token);
}

function sanitizeAdminInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeAdminInput', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Admin Authentication Functions
 */
function isAdminLoggedIn() {
    return isset($_SESSION['admin_user_id']) && 
           isset($_SESSION['admin_username']) &&
           isset($_SESSION['admin_login_time']) &&
           (time() - $_SESSION['admin_login_time']) < ADMIN_SESSION_TIMEOUT;
}

function requireAdminLogin() {
    if (!isAdminLoggedIn()) {
        adminRedirect('login.php');
    }
}

function adminRedirect($location) {
    if (!headers_sent()) {
        header("Location: $location");
        exit();
    } else {
        echo "<script>window.location.href='$location';</script>";
        exit();
    }
}

function adminLogout() {
    // Clear admin session variables
    unset($_SESSION['admin_user_id']);
    unset($_SESSION['admin_username']);
    unset($_SESSION['admin_full_name']);
    unset($_SESSION['admin_login_time']);
    unset($_SESSION[ADMIN_CSRF_TOKEN_NAME]);
    
    // Destroy session if no other data
    if (empty($_SESSION)) {
        session_destroy();
    }
}

/**
 * Admin Utility Functions
 */
function logAdminActivity($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $user = $_SESSION['admin_username'] ?? 'unknown';
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    $log_entry = "[$timestamp] [$level] [ADMIN:$user] [IP:$ip] $message" . PHP_EOL;
    
    // Try to write to log file
    $log_file = '../logs/admin.log';
    if (is_dir('../logs') || mkdir('../logs', 0755, true)) {
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }
    
    // Also log to PHP error log
    error_log("ADMIN: $log_entry");
}

/**
 * Admin Authentication Function
 */
function authenticateAdminUser($username, $password) {
    try {
        $db = AdminDatabase::getInstance();
        $conn = $db->getConnection();
        
        $sql = "SELECT id, username, password_hash, full_name FROM admin_users WHERE username = ? AND is_active = 1";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 1) {
            $user = $result->fetch_assoc();
            
            if (password_verify($password, $user['password_hash'])) {
                // Set session variables
                $_SESSION['admin_user_id'] = $user['id'];
                $_SESSION['admin_username'] = $user['username'];
                $_SESSION['admin_full_name'] = $user['full_name'];
                $_SESSION['admin_login_time'] = time();
                
                // Update last login
                $update_sql = "UPDATE admin_users SET last_login = NOW() WHERE id = ?";
                $update_stmt = $conn->prepare($update_sql);
                $update_stmt->bind_param("i", $user['id']);
                $update_stmt->execute();
                
                return true;
            }
        }
        
        return false;
        
    } catch (Exception $e) {
        logAdminActivity("Authentication error: " . $e->getMessage(), 'ERROR');
        return false;
    }
}

// Set timezone
date_default_timezone_set('Pacific/Guadalcanal');
?>
