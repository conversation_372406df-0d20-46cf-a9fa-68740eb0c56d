/**
 * Solomon Islands Scholarship Application - JavaScript
 * Clean Working Version
 */

// Global form state
window.scholarshipForm = {
    currentStep: 1,
    totalSteps: 5,
    form: null,
    steps: null,
    progressBar: null,
    nextBtn: null,
    prevBtn: null,
    submitBtn: null
};

/**
 * Initialize form when DOM is ready
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 DOM loaded, starting initialization...');

    // Get form elements
    const form = document.getElementById('scholarshipForm');
    const steps = document.querySelectorAll('.form-step');
    const progressBar = document.querySelector('.progress-bar');
    const nextBtn = document.getElementById('nextBtn');
    const prevBtn = document.getElementById('prevBtn');
    const submitBtn = document.getElementById('submitBtn');

    console.log('📋 Found elements:', {
        form: !!form,
        steps: steps.length,
        progressBar: !!progressBar,
        nextBtn: !!nextBtn,
        prevBtn: !!prevBtn,
        submitBtn: !!submitBtn
    });

    // Check if required elements exist
    if (!form) {
        console.error('❌ Form element not found!');
        return;
    }

    if (steps.length === 0) {
        console.error('❌ No form steps found!');
        return;
    }

    if (!nextBtn) {
        console.error('❌ Next button not found!');
        return;
    }

    // Update global state
    window.scholarshipForm.form = form;
    window.scholarshipForm.steps = steps;
    window.scholarshipForm.totalSteps = steps.length;
    window.scholarshipForm.progressBar = progressBar;
    window.scholarshipForm.nextBtn = nextBtn;
    window.scholarshipForm.prevBtn = prevBtn;
    window.scholarshipForm.submitBtn = submitBtn;

    console.log('✅ Form state updated:', window.scholarshipForm);

    // Add event listeners
    setupEventListeners();

    // Show first step
    showStep(1);

    console.log('🎉 Form initialization complete!');
});

/**
 * Setup event listeners
 */
function setupEventListeners() {
    const { nextBtn, prevBtn, form } = window.scholarshipForm;

    console.log('🔗 Setting up event listeners...');

    // Next button
    if (nextBtn) {
        nextBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('➡️ Next button clicked');
            handleNext();
        });
        console.log('✅ Next button listener added');
    }

    // Previous button
    if (prevBtn) {
        prevBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('⬅️ Previous button clicked');
            handlePrevious();
        });
        console.log('✅ Previous button listener added');
    }

    // Form submission
    if (form) {
        form.addEventListener('submit', function(e) {
            console.log('📤 Form submission attempted');
            // For now, allow submission (add validation later)
        });
        console.log('✅ Form submission listener added');
    }
}

/**
 * Show specific step
 */
function showStep(stepNumber) {
    const { steps, totalSteps, progressBar, nextBtn, prevBtn, submitBtn } = window.scholarshipForm;

    console.log('🎯 showStep called with step:', stepNumber);

    if (!steps || steps.length === 0) {
        console.error('❌ No steps found!');
        return;
    }

    // Validate step number
    if (stepNumber < 1 || stepNumber > totalSteps) {
        console.error('❌ Invalid step number:', stepNumber);
        return;
    }

    // Hide all steps
    steps.forEach((step, index) => {
        step.classList.remove('active');
        console.log('👻 Hiding step', index + 1);
    });

    // Show target step
    const targetStep = steps[stepNumber - 1];
    if (targetStep) {
        targetStep.classList.add('active');
        window.scholarshipForm.currentStep = stepNumber;
        console.log('✨ Showing step', stepNumber);
    } else {
        console.error('❌ Target step not found:', stepNumber);
        return;
    }

    // Update navigation buttons
    if (prevBtn) {
        prevBtn.style.display = stepNumber > 1 ? 'block' : 'none';
        console.log('⬅️ Previous button:', stepNumber > 1 ? 'visible' : 'hidden');
    }

    if (nextBtn && submitBtn) {
        if (stepNumber === totalSteps) {
            nextBtn.style.display = 'none';
            submitBtn.style.display = 'block';
            console.log('📤 Submit button visible');
        } else {
            nextBtn.style.display = 'block';
            submitBtn.style.display = 'none';
            console.log('➡️ Next button visible');
        }
    }

    // Update progress bar
    if (progressBar) {
        const percentage = (stepNumber / totalSteps) * 100;
        progressBar.style.width = percentage + '%';
        progressBar.setAttribute('aria-valuenow', percentage);
        console.log('📊 Progress updated to', percentage + '%');
    }

    console.log('🎉 Step', stepNumber, 'is now active');
}

/**
 * Handle next button click
 */
function handleNext() {
    const { currentStep, totalSteps } = window.scholarshipForm;

    console.log('🚀 handleNext: current step is', currentStep);

    // Simple validation - just check required fields
    if (!simpleValidation()) {
        console.log('❌ Validation failed for step', currentStep);
        return;
    }

    // Move to next step
    if (currentStep < totalSteps) {
        const nextStep = currentStep + 1;
        console.log('➡️ Moving to step', nextStep);
        showStep(nextStep);
    } else {
        console.log('⚠️ Already at last step');
    }
}

/**
 * Handle previous button click
 */
function handlePrevious() {
    const { currentStep } = window.scholarshipForm;

    console.log('🔙 handlePrevious: current step is', currentStep);

    if (currentStep > 1) {
        const prevStep = currentStep - 1;
        console.log('⬅️ Moving to step', prevStep);
        showStep(prevStep);
    } else {
        console.log('⚠️ Already at first step');
    }
}

/**
 * Simple validation for testing
 */
function simpleValidation() {
    const { currentStep, steps } = window.scholarshipForm;

    if (!steps || !steps[currentStep - 1]) {
        console.log('❌ Current step element not found');
        return false;
    }

    const currentStepElement = steps[currentStep - 1];
    const requiredFields = currentStepElement.querySelectorAll('[required]');

    console.log('🔍 Checking', requiredFields.length, 'required fields in step', currentStep);

    let isValid = true;

    requiredFields.forEach((field, index) => {
        if (field.type === 'checkbox') {
            if (!field.checked) {
                console.log('❌ Checkbox not checked:', field.name || field.id);
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            }
        } else if (!field.value.trim()) {
            console.log('❌ Field empty:', field.name || field.id);
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        }
    });

    // Special check for step 2 province checkboxes
    if (currentStep === 2) {
        const provinceCheckboxes = currentStepElement.querySelectorAll('input[name="province_of_origin[]"]');
        const isProvinceSelected = Array.from(provinceCheckboxes).some(cb => cb.checked);

        if (!isProvinceSelected) {
            console.log('❌ No province selected');
            const errorElement = document.getElementById('province_origin_error');
            if (errorElement) {
                errorElement.style.display = 'block';
            }
            isValid = false;
        } else {
            const errorElement = document.getElementById('province_origin_error');
            if (errorElement) {
                errorElement.style.display = 'none';
            }
        }
    }

    console.log('✅ Validation result for step', currentStep, ':', isValid);
    return isValid;
}

// 🎉 Clean JavaScript file complete!
