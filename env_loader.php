<?php
/**
 * Simple Environment Variable Loader
 * Loads environment variables from .env file
 */

class EnvLoader {
    
    /**
     * Load environment variables from .env file
     */
    public static function load($file_path = '.env') {
        // Try multiple possible locations for .env file
        $possible_paths = [
            $file_path,
            __DIR__ . '/' . basename($file_path),
            dirname(__FILE__) . '/' . basename($file_path),
            $_SERVER['DOCUMENT_ROOT'] . '/solomonislands/' . basename($file_path),
            realpath('.') . '/' . basename($file_path)
        ];

        $found_file = null;
        foreach ($possible_paths as $path) {
            if (file_exists($path)) {
                $found_file = $path;
                break;
            }
        }

        if (!$found_file) {
            // Create a default .env file if none exists
            self::createDefaultEnvFile($file_path);
            if (!file_exists($file_path)) {
                throw new Exception(".env file not found. Tried paths: " . implode(', ', $possible_paths));
            }
            $found_file = $file_path;
        }

        $lines = file($found_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            // Skip comments
            if (strpos(trim($line), '#') === 0) {
                continue;
            }
            
            // Parse key=value pairs
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                
                $key = trim($key);
                $value = trim($value);
                
                // Remove quotes if present
                if (preg_match('/^"(.*)"$/', $value, $matches)) {
                    $value = $matches[1];
                } elseif (preg_match("/^'(.*)'$/", $value, $matches)) {
                    $value = $matches[1];
                }
                
                // Set environment variable
                if (!array_key_exists($key, $_ENV)) {
                    $_ENV[$key] = $value;
                    putenv("$key=$value");
                }
            }
        }
    }
    
    /**
     * Get environment variable with optional default
     */
    public static function get($key, $default = null) {
        return $_ENV[$key] ?? getenv($key) ?: $default;
    }
    
    /**
     * Get environment variable as boolean
     */
    public static function getBool($key, $default = false) {
        $value = self::get($key, $default);
        
        if (is_bool($value)) {
            return $value;
        }
        
        return in_array(strtolower($value), ['true', '1', 'yes', 'on']);
    }
    
    /**
     * Get environment variable as integer
     */
    public static function getInt($key, $default = 0) {
        return (int) self::get($key, $default);
    }

    /**
     * Create a default .env file with your database credentials
     */
    private static function createDefaultEnvFile($file_path) {
        $default_content = '# Solomon Islands Scholarship Application - Environment Configuration
# IMPORTANT: Never commit this file to version control!

# Database Configuration
DB_HOST=localhost
DB_USERNAME=u787474055_solomonislands
DB_PASSWORD=Blackpanther707@707
DB_NAME=u787474055_solomonislands

# Application Configuration
APP_NAME="Solomon Islands Scholarship Application"
APP_VERSION=1.0.0
APP_ENV=production
APP_DEBUG=false

# Security Configuration
CSRF_TOKEN_NAME=csrf_token
SESSION_TIMEOUT=3600

# Email Configuration (Optional - Configure if you want email functionality)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
FROM_EMAIL=<EMAIL>
FROM_NAME="Solomon Islands Scholarship Program"
ADMIN_EMAIL=<EMAIL>

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_DIR=uploads/

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/application.log

# Admin Configuration
ADMIN_SESSION_TIMEOUT=7200
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD_HASH=$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi

# Timezone
APP_TIMEZONE=Pacific/Guadalcanal
';

        try {
            file_put_contents($file_path, $default_content);
        } catch (Exception $e) {
            // Silently fail if we can't create the file
        }
    }
}
?>
