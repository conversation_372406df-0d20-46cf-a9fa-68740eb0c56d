<?php
// Test the admin configuration and database connection
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Admin Configuration Test</h2>";

// Test 1: Load config.php
echo "<h3>1. Testing config.php loading</h3>";
try {
    require_once 'config.php';
    echo "<div style='color: green;'>✅ config.php loaded successfully</div>";
    echo "<div>DB_HOST: " . (defined('DB_HOST') ? DB_HOST : 'Not defined') . "</div>";
    echo "<div>DB_USERNAME: " . (defined('DB_USERNAME') ? DB_USERNAME : 'Not defined') . "</div>";
    echo "<div>DB_NAME: " . (defined('DB_NAME') ? DB_NAME : 'Not defined') . "</div>";
    echo "<div>APP_NAME: " . (defined('APP_NAME') ? APP_NAME : 'Not defined') . "</div>";
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ config.php loading failed: " . $e->getMessage() . "</div>";
    exit;
}

// Test 2: Test Database class
echo "<h3>2. Testing Database class</h3>";
try {
    $db = Database::getInstance();
    echo "<div style='color: green;'>✅ Database instance created</div>";
    
    $conn = $db->getConnection();
    echo "<div style='color: green;'>✅ Database connection obtained</div>";
    
    // Test a simple query
    $result = $conn->query("SELECT 1 as test");
    if ($result) {
        echo "<div style='color: green;'>✅ Database query test successful</div>";
    } else {
        echo "<div style='color: red;'>❌ Database query test failed</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Database class error: " . $e->getMessage() . "</div>";
}

// Test 3: Check admin_users table
echo "<h3>3. Testing admin_users table access</h3>";
try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    $sql = "SELECT COUNT(*) as count FROM admin_users";
    $result = $conn->query($sql);
    
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "<div style='color: green;'>✅ admin_users table accessible - found $count users</div>";
        
        // Check if admin user exists
        $admin_sql = "SELECT id, username, full_name FROM admin_users WHERE username = 'admin'";
        $admin_result = $conn->query($admin_sql);
        
        if ($admin_result && $admin_result->num_rows > 0) {
            $admin_user = $admin_result->fetch_assoc();
            echo "<div style='color: green;'>✅ Admin user found:</div>";
            echo "<div>ID: " . $admin_user['id'] . "</div>";
            echo "<div>Username: " . $admin_user['username'] . "</div>";
            echo "<div>Full Name: " . $admin_user['full_name'] . "</div>";
        } else {
            echo "<div style='color: red;'>❌ Admin user not found</div>";
        }
        
    } else {
        echo "<div style='color: red;'>❌ Cannot access admin_users table: " . $conn->error . "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ admin_users table test error: " . $e->getMessage() . "</div>";
}

// Test 4: Test admin authentication function
echo "<h3>4. Testing admin authentication</h3>";
if (function_exists('authenticateAdmin')) {
    echo "<div style='color: green;'>✅ authenticateAdmin function exists</div>";
} else {
    echo "<div style='color: red;'>❌ authenticateAdmin function not found</div>";
}

// Test 5: Test session functions
echo "<h3>5. Testing session functions</h3>";
if (function_exists('isAdminLoggedIn')) {
    echo "<div style='color: green;'>✅ isAdminLoggedIn function exists</div>";
    echo "<div>Currently logged in: " . (isAdminLoggedIn() ? 'Yes' : 'No') . "</div>";
} else {
    echo "<div style='color: red;'>❌ isAdminLoggedIn function not found</div>";
}

// Test 6: Test CSRF functions
echo "<h3>6. Testing CSRF functions</h3>";
if (function_exists('generateCSRFToken')) {
    echo "<div style='color: green;'>✅ generateCSRFToken function exists</div>";
    $token = generateCSRFToken();
    echo "<div>Generated token: " . substr($token, 0, 20) . "...</div>";
} else {
    echo "<div style='color: red;'>❌ generateCSRFToken function not found</div>";
}

// Test 7: Simulate admin login attempt
echo "<h3>7. Simulating admin login attempt</h3>";
try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    $sql = "SELECT id, username, password_hash, full_name FROM admin_users WHERE username = ? AND is_active = 1";
    $stmt = $conn->prepare($sql);
    $username = 'admin';
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 1) {
        $user = $result->fetch_assoc();
        echo "<div style='color: green;'>✅ Admin user found for login</div>";
        
        // Test password verification
        if (password_verify('admin123', $user['password_hash'])) {
            echo "<div style='color: green;'>✅ Password verification successful</div>";
        } else {
            echo "<div style='color: red;'>❌ Password verification failed</div>";
        }
    } else {
        echo "<div style='color: red;'>❌ Admin user not found or inactive</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Login simulation error: " . $e->getMessage() . "</div>";
}

echo "<h3>8. Summary</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>Admin Login Options:</h4>";
echo "<div><strong>Original Admin Login:</strong> <a href='admin/login.php' target='_blank'>admin/login.php</a></div>";
echo "<div><strong>Direct Admin Login:</strong> <a href='admin/login_direct.php' target='_blank'>admin/login_direct.php</a></div>";
echo "<div><strong>Credentials:</strong> admin / admin123</div>";
echo "</div>";
?>
