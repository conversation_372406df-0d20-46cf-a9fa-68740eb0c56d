<?php
// Simple debug page to test form functionality
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Debug - Solomon Islands Scholarship</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/style.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <h2>Form Debug Information</h2>
                
                <div class="alert alert-info">
                    <h5>Real-time Debug Info</h5>
                    <div id="debugInfo">Loading...</div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5>Test Form Navigation</h5>
                    </div>
                    <div class="card-body">
                        <form id="scholarshipForm">
                            <!-- Progress Bar -->
                            <div class="progress mb-4" style="height: 8px;">
                                <div class="progress-bar" role="progressbar" style="width: 20%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>

                            <!-- Step 1 -->
                            <div class="form-step active" data-step="1">
                                <h4 class="text-primary mb-4">Step 1: Basic Information</h4>
                                <div class="mb-3">
                                    <label for="student_code" class="form-label">Student Code *</label>
                                    <input type="text" class="form-control" id="student_code" name="student_code" required>
                                </div>
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" required>
                                </div>
                            </div>

                            <!-- Step 2 -->
                            <div class="form-step" data-step="2">
                                <h4 class="text-primary mb-4">Step 2: Location Information</h4>
                                <div class="mb-3">
                                    <label class="form-label">Province of Origin *</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="province_of_origin[]" value="Central" id="central">
                                        <label class="form-check-label" for="central">Central</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="province_of_origin[]" value="Honiara" id="honiara">
                                        <label class="form-check-label" for="honiara">Honiara</label>
                                    </div>
                                    <div class="invalid-feedback d-block" id="province_origin_error" style="display: none;">
                                        Please select at least one province of origin.
                                    </div>
                                </div>
                            </div>

                            <!-- Step 3 -->
                            <div class="form-step" data-step="3">
                                <h4 class="text-primary mb-4">Step 3: Travel Information</h4>
                                <div class="mb-3">
                                    <label for="transport" class="form-label">Mode of Transport *</label>
                                    <select class="form-select" id="transport" name="transport" required>
                                        <option value="">Choose...</option>
                                        <option value="Air">Air</option>
                                        <option value="Sea">Sea</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Step 4 -->
                            <div class="form-step" data-step="4">
                                <h4 class="text-primary mb-4">Step 4: Academic Information</h4>
                                <div class="mb-3">
                                    <label for="qualifications" class="form-label">Academic Qualifications *</label>
                                    <textarea class="form-control" id="qualifications" name="qualifications" required></textarea>
                                </div>
                            </div>

                            <!-- Step 5 -->
                            <div class="form-step" data-step="5">
                                <h4 class="text-primary mb-4">Step 5: Final Details</h4>
                                <div class="mb-3">
                                    <label for="motivation" class="form-label">Motivation Letter *</label>
                                    <textarea class="form-control" id="motivation" name="motivation" required></textarea>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                    <label class="form-check-label" for="terms">I agree to the terms *</label>
                                </div>
                            </div>

                            <!-- Navigation Buttons -->
                            <div class="d-flex justify-content-between mt-4">
                                <button type="button" class="btn btn-secondary" id="prevBtn" style="display: none;">
                                    <i class="fas fa-arrow-left me-2"></i>Previous
                                </button>
                                <button type="button" class="btn btn-primary" id="nextBtn">
                                    Next<i class="fas fa-arrow-right ms-2"></i>
                                </button>
                                <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">
                                    <i class="fas fa-paper-plane me-2"></i>Submit
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="mt-4">
                    <a href="application.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>Go to Real Application Form
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/script.js"></script>
    
    <script>
        // Debug information updater
        function updateDebugInfo() {
            const form = document.getElementById('scholarshipForm');
            const steps = document.querySelectorAll('.form-step');
            const nextBtn = document.getElementById('nextBtn');
            const prevBtn = document.getElementById('prevBtn');
            const submitBtn = document.getElementById('submitBtn');
            const activeStep = document.querySelector('.form-step.active');
            const progressBar = document.querySelector('.progress-bar');
            
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>Form Elements:</strong><br>
                        Form found: <span class="badge ${form ? 'bg-success' : 'bg-danger'}">${!!form}</span><br>
                        Steps found: <span class="badge bg-info">${steps.length}</span><br>
                        Next button: <span class="badge ${nextBtn ? 'bg-success' : 'bg-danger'}">${!!nextBtn}</span><br>
                        Previous button: <span class="badge ${prevBtn ? 'bg-success' : 'bg-danger'}">${!!prevBtn}</span><br>
                        Submit button: <span class="badge ${submitBtn ? 'bg-success' : 'bg-danger'}">${!!submitBtn}</span><br>
                    </div>
                    <div class="col-md-6">
                        <strong>Form State:</strong><br>
                        Current step: <span class="badge bg-primary">${window.scholarshipForm ? window.scholarshipForm.currentStep : 'Not initialized'}</span><br>
                        Total steps: <span class="badge bg-info">${window.scholarshipForm ? window.scholarshipForm.totalSteps : 'Unknown'}</span><br>
                        Active step: <span class="badge bg-warning">${activeStep?.dataset.step || 'None'}</span><br>
                        Progress bar width: <span class="badge bg-secondary">${progressBar?.style.width || 'Unknown'}</span><br>
                        Form initialized: <span class="badge ${window.scholarshipForm ? 'bg-success' : 'bg-danger'}">${!!window.scholarshipForm}</span>
                    </div>
                </div>
            `;
        }
        
        // Update debug info every second
        setInterval(updateDebugInfo, 1000);
        
        // Initial update
        setTimeout(updateDebugInfo, 100);
        
        // Log button clicks
        document.addEventListener('click', function(e) {
            if (e.target.id === 'nextBtn') {
                console.log('Next button clicked at:', new Date().toLocaleTimeString());
            }
            if (e.target.id === 'prevBtn') {
                console.log('Previous button clicked at:', new Date().toLocaleTimeString());
            }
        });
        
        // Log form initialization
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded at:', new Date().toLocaleTimeString());
        });
    </script>
</body>
</html>
