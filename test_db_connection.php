<?php
// Test database connection and admin tables
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Database Connection Test</h2>";

// Test 1: Direct connection with credentials
echo "<h3>1. Testing Direct Database Connection</h3>";
$host = 'localhost';
$username = 'u787474055_solomonislands';
$password = 'Blackpanther707@';
$database = 'u787474055_solomonislands';

try {
    $conn = new mysqli($host, $username, $password, $database);
    
    if ($conn->connect_error) {
        echo "<div style='color: red;'>❌ Direct connection failed: " . $conn->connect_error . "</div>";
    } else {
        echo "<div style='color: green;'>✅ Direct connection successful!</div>";
        echo "<div>Server info: " . $conn->server_info . "</div>";
        echo "<div>Host info: " . $conn->host_info . "</div>";
        
        // Test 2: Check if admin_users table exists
        echo "<h3>2. Checking Admin Tables</h3>";
        
        $tables_to_check = ['admin_users', 'admin_sessions', 'applications'];
        
        foreach ($tables_to_check as $table) {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            if ($result && $result->num_rows > 0) {
                echo "<div style='color: green;'>✅ Table '$table' exists</div>";
                
                if ($table === 'admin_users') {
                    // Check if admin user exists
                    $admin_check = $conn->query("SELECT COUNT(*) as count FROM admin_users WHERE username = 'admin'");
                    if ($admin_check) {
                        $admin_count = $admin_check->fetch_assoc()['count'];
                        if ($admin_count > 0) {
                            echo "<div style='color: green;'>✅ Admin user exists</div>";
                        } else {
                            echo "<div style='color: orange;'>⚠️ Admin user does not exist</div>";
                        }
                    }
                }
            } else {
                echo "<div style='color: red;'>❌ Table '$table' does not exist</div>";
            }
        }
        
        $conn->close();
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Exception: " . $e->getMessage() . "</div>";
}

// Test 3: Test config.php loading
echo "<h3>3. Testing Config.php Loading</h3>";
try {
    require_once 'config.php';
    echo "<div style='color: green;'>✅ config.php loaded successfully</div>";
    echo "<div>DB_HOST: " . DB_HOST . "</div>";
    echo "<div>DB_USERNAME: " . DB_USERNAME . "</div>";
    echo "<div>DB_NAME: " . DB_NAME . "</div>";
    
    // Test Database class
    echo "<h3>4. Testing Database Class</h3>";
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();
        echo "<div style='color: green;'>✅ Database class working</div>";
        
        // Test admin_users table query
        $sql = "SELECT COUNT(*) as count FROM admin_users";
        $result = $conn->query($sql);
        if ($result) {
            $count = $result->fetch_assoc()['count'];
            echo "<div style='color: green;'>✅ Admin users table accessible, found $count users</div>";
        } else {
            echo "<div style='color: red;'>❌ Cannot query admin_users table</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ Database class error: " . $e->getMessage() . "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Config loading error: " . $e->getMessage() . "</div>";
}

// Test 4: Check if we can create admin user
echo "<h3>5. Admin User Creation Test</h3>";
try {
    $conn = new mysqli($host, $username, $password, $database);
    
    // Check if admin_users table exists, if not create it
    $table_check = $conn->query("SHOW TABLES LIKE 'admin_users'");
    if (!$table_check || $table_check->num_rows === 0) {
        echo "<div style='color: orange;'>⚠️ Creating admin_users table...</div>";
        
        $create_table_sql = "
        CREATE TABLE admin_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            email VARCHAR(100),
            full_name VARCHAR(100),
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        )";
        
        if ($conn->query($create_table_sql)) {
            echo "<div style='color: green;'>✅ admin_users table created</div>";
        } else {
            echo "<div style='color: red;'>❌ Failed to create admin_users table: " . $conn->error . "</div>";
        }
    }
    
    // Check if admin user exists, if not create it
    $admin_check = $conn->query("SELECT COUNT(*) as count FROM admin_users WHERE username = 'admin'");
    if ($admin_check) {
        $admin_count = $admin_check->fetch_assoc()['count'];
        if ($admin_count === 0) {
            echo "<div style='color: orange;'>⚠️ Creating default admin user...</div>";
            
            $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
            $insert_sql = "INSERT INTO admin_users (username, password_hash, email, full_name) VALUES (?, ?, ?, ?)";
            $stmt = $conn->prepare($insert_sql);
            $email = '<EMAIL>';
            $full_name = 'System Administrator';
            $username_val = 'admin';
            
            $stmt->bind_param("ssss", $username_val, $password_hash, $email, $full_name);
            
            if ($stmt->execute()) {
                echo "<div style='color: green;'>✅ Default admin user created successfully</div>";
                echo "<div>Username: admin</div>";
                echo "<div>Password: admin123</div>";
            } else {
                echo "<div style='color: red;'>❌ Failed to create admin user: " . $conn->error . "</div>";
            }
        } else {
            echo "<div style='color: green;'>✅ Admin user already exists</div>";
        }
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Admin user creation error: " . $e->getMessage() . "</div>";
}

echo "<h3>6. Summary</h3>";
echo "<div>If all tests pass, try logging into admin again with:</div>";
echo "<div><strong>Username:</strong> admin</div>";
echo "<div><strong>Password:</strong> admin123</div>";
echo "<div><strong>URL:</strong> <a href='admin/login.php'>admin/login.php</a></div>";
?>
