<?php
/**
 * Fixed Form Submission Handler
 * Handles form submission with proper database field mapping
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    redirect('application.php');
}

// Validate CSRF token
if (!isset($_POST['csrf_token']) || !validateCSRFToken($_POST['csrf_token'])) {
    die('Security token validation failed. Please try again.');
}

try {
    // Get database connection
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    // Sanitize and validate input data
    $student_code = sanitizeInput($_POST['student_code'] ?? '');
    $full_name = sanitizeInput($_POST['full_name'] ?? '');
    $date_of_birth = !empty($_POST['date_of_birth']) ? $_POST['date_of_birth'] : null;
    $gender = sanitizeInput($_POST['gender'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    
    // Handle province arrays (convert to comma-separated strings for SET fields)
    $province_of_origin = '';
    if (isset($_POST['province_of_origin']) && is_array($_POST['province_of_origin'])) {
        $province_of_origin = implode(',', $_POST['province_of_origin']);
    }
    
    $current_residential_province = '';
    if (isset($_POST['current_residential_province']) && is_array($_POST['current_residential_province'])) {
        $current_residential_province = implode(',', $_POST['current_residential_province']);
    }
    
    // Handle HEI selection (convert array to comma-separated string for SET field)
    $hei_name = '';
    if (isset($_POST['hei_name']) && is_array($_POST['hei_name'])) {
        $hei_name = implode(',', $_POST['hei_name']);
    } elseif (isset($_POST['hei_name']) && !empty($_POST['hei_name'])) {
        $hei_name = sanitizeInput($_POST['hei_name']);
    }
    
    // Other fields
    $mode_of_transport_to_honiara = sanitizeInput($_POST['mode_of_transport_to_honiara'] ?? '');
    $honiara_pom_route = sanitizeInput($_POST['honiara_pom_route'] ?? '');
    $png_province = sanitizeInput($_POST['png_province'] ?? '');
    $travel_question = sanitizeInput($_POST['travel_question'] ?? '');
    $estimated_cost = !empty($_POST['estimated_cost']) ? floatval($_POST['estimated_cost']) : null;
    $academic_qualifications = sanitizeInput($_POST['academic_qualifications'] ?? '');
    $course_of_study = sanitizeInput($_POST['course_of_study'] ?? '');
    $motivation_letter = sanitizeInput($_POST['motivation_letter'] ?? '');
    
    // System fields
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    
    // Validate required fields
    $errors = [];
    
    if (empty($student_code)) $errors[] = 'Student Code is required';
    if (empty($full_name)) $errors[] = 'Full Name is required';
    if (empty($province_of_origin)) $errors[] = 'Province of Origin is required';
    if (empty($mode_of_transport_to_honiara)) $errors[] = 'Mode of Transport is required';
    if (empty($hei_name)) $errors[] = 'Higher Education Institution is required';
    if (empty($png_province)) $errors[] = 'PNG Province is required';
    if (empty($academic_qualifications)) $errors[] = 'Academic Qualifications are required';
    if (empty($motivation_letter)) $errors[] = 'Motivation Letter is required';
    
    // Check terms agreement
    if (!isset($_POST['terms_agreement']) || $_POST['terms_agreement'] !== '1') {
        $errors[] = 'You must agree to the terms and conditions';
    }
    
    if (!empty($errors)) {
        $error_message = implode('<br>', $errors);
        echo "<!DOCTYPE html>
        <html>
        <head>
            <title>Submission Error</title>
            <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
        </head>
        <body>
            <div class='container py-5'>
                <div class='alert alert-danger'>
                    <h4>❌ Submission Failed</h4>
                    <p>Please correct the following errors:</p>
                    <div>$error_message</div>
                    <div class='mt-3'>
                        <a href='application.php' class='btn btn-primary'>Go Back to Form</a>
                    </div>
                </div>
            </div>
        </body>
        </html>";
        exit;
    }
    
    // Prepare SQL statement
    $sql = "INSERT INTO applications (
        student_code, selection_status, full_name, date_of_birth, gender, email, phone,
        province_of_origin, current_residential_province, mode_of_transport_to_honiara,
        honiara_pom_route, hei_name, png_province, travel_question, estimated_cost,
        academic_qualifications, course_of_study, motivation_letter,
        ip_address, user_agent, created_at
    ) VALUES (?, 'Pending', ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        throw new Exception("Database prepare failed: " . $conn->error);
    }
    
    // Bind parameters
    $stmt->bind_param(
        "sssssssssssssdsssss",
        $student_code,
        $full_name,
        $date_of_birth,
        $gender,
        $email,
        $phone,
        $province_of_origin,
        $current_residential_province,
        $mode_of_transport_to_honiara,
        $honiara_pom_route,
        $hei_name,
        $png_province,
        $travel_question,
        $estimated_cost,
        $academic_qualifications,
        $course_of_study,
        $motivation_letter,
        $ip_address,
        $user_agent
    );
    
    // Execute the statement
    if ($stmt->execute()) {
        $application_id = $conn->insert_id;
        
        // Log the successful submission
        logActivity("New scholarship application submitted: ID $application_id, Student: $student_code", 'INFO');
        
        // Success page
        echo "<!DOCTYPE html>
        <html lang='en'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Application Submitted Successfully</title>
            <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
            <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
        </head>
        <body>
            <div class='container py-5'>
                <div class='row justify-content-center'>
                    <div class='col-md-8'>
                        <div class='card border-success'>
                            <div class='card-header bg-success text-white text-center'>
                                <h3><i class='fas fa-check-circle me-2'></i>Application Submitted Successfully!</h3>
                            </div>
                            <div class='card-body'>
                                <div class='alert alert-success'>
                                    <h4>✅ Thank you, $full_name!</h4>
                                    <p>Your scholarship application has been successfully submitted.</p>
                                </div>
                                
                                <div class='row'>
                                    <div class='col-md-6'>
                                        <h5>📋 Application Details:</h5>
                                        <ul class='list-unstyled'>
                                            <li><strong>Application ID:</strong> $application_id</li>
                                            <li><strong>Student Code:</strong> $student_code</li>
                                            <li><strong>Full Name:</strong> $full_name</li>
                                            <li><strong>Status:</strong> <span class='badge bg-warning'>Pending Review</span></li>
                                            <li><strong>Submitted:</strong> " . date('F j, Y g:i A') . "</li>
                                        </ul>
                                    </div>
                                    <div class='col-md-6'>
                                        <h5>📧 What's Next?</h5>
                                        <ul>
                                            <li>Your application is now under review</li>
                                            <li>You will be contacted via email if additional information is needed</li>
                                            <li>Results will be announced according to the scholarship timeline</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class='alert alert-info mt-4'>
                                    <h6><i class='fas fa-info-circle me-2'></i>Important Notes:</h6>
                                    <ul class='mb-0'>
                                        <li>Please save your Application ID: <strong>$application_id</strong></li>
                                        <li>You can check your application status using the status checker</li>
                                        <li>Keep this page for your records</li>
                                    </ul>
                                </div>
                                
                                <div class='text-center mt-4'>
                                    <a href='status.php' class='btn btn-primary me-2'>
                                        <i class='fas fa-search me-2'></i>Check Application Status
                                    </a>
                                    <a href='home.php' class='btn btn-outline-secondary'>
                                        <i class='fas fa-home me-2'></i>Return to Home
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>";
        
    } else {
        throw new Exception("Database execution failed: " . $stmt->error);
    }
    
} catch (Exception $e) {
    // Log the error
    logActivity("Scholarship application submission failed: " . $e->getMessage(), 'ERROR');
    
    // Error page
    echo "<!DOCTYPE html>
    <html>
    <head>
        <title>Submission Error</title>
        <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    </head>
    <body>
        <div class='container py-5'>
            <div class='alert alert-danger'>
                <h4>❌ Submission Failed</h4>
                <p>We're sorry, but there was an error processing your application.</p>
                <p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
                <div class='mt-3'>
                    <a href='application.php' class='btn btn-primary'>Try Again</a>
                    <a href='home.php' class='btn btn-outline-secondary ms-2'>Return to Home</a>
                </div>
            </div>
        </div>
    </body>
    </html>";
}
?>
