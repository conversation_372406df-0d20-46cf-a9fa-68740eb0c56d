<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Navigation Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/style.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <h2>Form Navigation Test</h2>
        
        <div class="alert alert-info">
            <h5>Debug Information</h5>
            <div id="debugInfo">Loading...</div>
        </div>

        <div class="card">
            <div class="card-body">
                <form id="scholarshipForm">
                    <!-- Progress Bar -->
                    <div class="progress mb-4" style="height: 8px;">
                        <div class="progress-bar" role="progressbar" style="width: 20%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>

                    <!-- Step 1 -->
                    <div class="form-step active" data-step="1">
                        <h4>Step 1: Basic Information</h4>
                        <div class="mb-3">
                            <label for="student_code" class="form-label">Student Code *</label>
                            <input type="text" class="form-control" id="student_code" name="student_code" required>
                        </div>
                        <div class="mb-3">
                            <label for="full_name" class="form-label">Full Name *</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                        </div>
                    </div>

                    <!-- Step 2 -->
                    <div class="form-step" data-step="2">
                        <h4>Step 2: Location Information</h4>
                        <div class="mb-3">
                            <label class="form-label">Province of Origin *</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="province_of_origin[]" value="Central" id="central">
                                <label class="form-check-label" for="central">Central</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="province_of_origin[]" value="Honiara" id="honiara">
                                <label class="form-check-label" for="honiara">Honiara</label>
                            </div>
                            <div class="invalid-feedback d-block" id="province_origin_error" style="display: none;">
                                Please select at least one province of origin.
                            </div>
                        </div>
                    </div>

                    <!-- Step 3 -->
                    <div class="form-step" data-step="3">
                        <h4>Step 3: Travel Information</h4>
                        <div class="mb-3">
                            <label for="transport" class="form-label">Mode of Transport *</label>
                            <select class="form-select" id="transport" name="transport" required>
                                <option value="">Choose...</option>
                                <option value="Air">Air</option>
                                <option value="Sea">Sea</option>
                            </select>
                        </div>
                    </div>

                    <!-- Step 4 -->
                    <div class="form-step" data-step="4">
                        <h4>Step 4: Academic Information</h4>
                        <div class="mb-3">
                            <label for="qualifications" class="form-label">Academic Qualifications *</label>
                            <textarea class="form-control" id="qualifications" name="qualifications" required></textarea>
                        </div>
                    </div>

                    <!-- Step 5 -->
                    <div class="form-step" data-step="5">
                        <h4>Step 5: Final Details</h4>
                        <div class="mb-3">
                            <label for="motivation" class="form-label">Motivation Letter *</label>
                            <textarea class="form-control" id="motivation" name="motivation" required></textarea>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                            <label class="form-check-label" for="terms">I agree to the terms *</label>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="d-flex justify-content-between mt-4">
                        <button type="button" class="btn btn-secondary" id="prevBtn" style="display: none;">
                            Previous
                        </button>
                        <button type="button" class="btn btn-primary" id="nextBtn">
                            Next
                        </button>
                        <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">
                            Submit
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/script.js"></script>
    
    <script>
        // Additional debug information
        setInterval(function() {
            const form = document.getElementById('scholarshipForm');
            const steps = document.querySelectorAll('.form-step');
            const nextBtn = document.getElementById('nextBtn');
            const prevBtn = document.getElementById('prevBtn');
            const submitBtn = document.getElementById('submitBtn');
            
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.innerHTML = `
                <strong>Form Elements:</strong><br>
                Form found: ${!!form}<br>
                Steps found: ${steps.length}<br>
                Next button: ${!!nextBtn}<br>
                Previous button: ${!!prevBtn}<br>
                Submit button: ${!!submitBtn}<br>
                Current step: ${window.scholarshipForm ? window.scholarshipForm.currentStep : 'Not initialized'}<br>
                Active step: ${document.querySelector('.form-step.active')?.dataset.step || 'None'}<br>
                Progress bar width: ${document.querySelector('.progress-bar')?.style.width || 'Unknown'}
            `;
        }, 1000);
    </script>
</body>
</html>
