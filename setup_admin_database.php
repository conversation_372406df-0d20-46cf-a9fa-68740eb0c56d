<?php
// Setup admin database tables and default user
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Admin Database Setup</h2>";

// Database credentials
$host = 'localhost';
$username = 'u787474055_solomonislands';
$password = 'Blackpanther707@';
$database = 'u787474055_solomonislands';

try {
    $conn = new mysqli($host, $username, $password, $database);
    
    if ($conn->connect_error) {
        die("<div style='color: red;'>❌ Database connection failed: " . $conn->connect_error . "</div>");
    }
    
    echo "<div style='color: green;'>✅ Connected to database successfully</div>";
    
    // Set charset
    $conn->set_charset("utf8mb4");
    
    // 1. Create admin_users table
    echo "<h3>1. Creating admin_users table</h3>";
    $admin_users_sql = "
    CREATE TABLE IF NOT EXISTS admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        email VARCHAR(100),
        full_name VARCHAR(100),
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT TRUE,
        INDEX idx_username (username),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($admin_users_sql)) {
        echo "<div style='color: green;'>✅ admin_users table created/verified</div>";
    } else {
        echo "<div style='color: red;'>❌ Error creating admin_users table: " . $conn->error . "</div>";
    }
    
    // 2. Create admin_sessions table
    echo "<h3>2. Creating admin_sessions table</h3>";
    $admin_sessions_sql = "
    CREATE TABLE IF NOT EXISTS admin_sessions (
        id VARCHAR(128) PRIMARY KEY,
        user_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        INDEX idx_user_id (user_id),
        INDEX idx_expires_at (expires_at),
        FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($admin_sessions_sql)) {
        echo "<div style='color: green;'>✅ admin_sessions table created/verified</div>";
    } else {
        echo "<div style='color: red;'>❌ Error creating admin_sessions table: " . $conn->error . "</div>";
    }
    
    // 3. Ensure applications table exists (it should already exist)
    echo "<h3>3. Verifying applications table</h3>";
    $check_applications = $conn->query("SHOW TABLES LIKE 'applications'");
    if ($check_applications && $check_applications->num_rows > 0) {
        echo "<div style='color: green;'>✅ applications table exists</div>";
    } else {
        echo "<div style='color: orange;'>⚠️ applications table does not exist - this should be created by the main schema</div>";
    }
    
    // 4. Create default admin user
    echo "<h3>4. Creating default admin user</h3>";
    
    // Check if admin user already exists
    $check_admin = $conn->prepare("SELECT id FROM admin_users WHERE username = ?");
    $admin_username = 'admin';
    $check_admin->bind_param("s", $admin_username);
    $check_admin->execute();
    $result = $check_admin->get_result();
    
    if ($result->num_rows > 0) {
        echo "<div style='color: green;'>✅ Admin user already exists</div>";
        
        // Update password to ensure it's correct
        $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
        $update_sql = "UPDATE admin_users SET password_hash = ? WHERE username = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("ss", $password_hash, $admin_username);
        
        if ($update_stmt->execute()) {
            echo "<div style='color: green;'>✅ Admin password updated</div>";
        } else {
            echo "<div style='color: red;'>❌ Failed to update admin password</div>";
        }
    } else {
        // Create new admin user
        $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
        $email = '<EMAIL>';
        $full_name = 'System Administrator';
        
        $insert_sql = "INSERT INTO admin_users (username, password_hash, email, full_name, is_active) VALUES (?, ?, ?, ?, 1)";
        $insert_stmt = $conn->prepare($insert_sql);
        $insert_stmt->bind_param("ssss", $admin_username, $password_hash, $email, $full_name);
        
        if ($insert_stmt->execute()) {
            echo "<div style='color: green;'>✅ Default admin user created successfully</div>";
        } else {
            echo "<div style='color: red;'>❌ Failed to create admin user: " . $conn->error . "</div>";
        }
    }
    
    // 5. Test admin authentication
    echo "<h3>5. Testing admin authentication</h3>";
    
    $test_sql = "SELECT id, username, password_hash, full_name FROM admin_users WHERE username = ? AND is_active = 1";
    $test_stmt = $conn->prepare($test_sql);
    $test_stmt->bind_param("s", $admin_username);
    $test_stmt->execute();
    $test_result = $test_stmt->get_result();
    
    if ($test_result->num_rows === 1) {
        $user = $test_result->fetch_assoc();
        
        if (password_verify('admin123', $user['password_hash'])) {
            echo "<div style='color: green;'>✅ Admin authentication test successful</div>";
            echo "<div><strong>User ID:</strong> " . $user['id'] . "</div>";
            echo "<div><strong>Username:</strong> " . $user['username'] . "</div>";
            echo "<div><strong>Full Name:</strong> " . $user['full_name'] . "</div>";
        } else {
            echo "<div style='color: red;'>❌ Password verification failed</div>";
        }
    } else {
        echo "<div style='color: red;'>❌ Admin user not found or inactive</div>";
    }
    
    // 6. Display summary
    echo "<h3>6. Setup Summary</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Admin Login Credentials:</h4>";
    echo "<div><strong>URL:</strong> <a href='admin/login.php' target='_blank'>admin/login.php</a></div>";
    echo "<div><strong>Username:</strong> admin</div>";
    echo "<div><strong>Password:</strong> admin123</div>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>⚠️ Security Notice:</h4>";
    echo "<div>Remember to change the default password 'admin123' to something secure before using in production!</div>";
    echo "</div>";
    
    // 7. Test database connection using config.php
    echo "<h3>7. Testing config.php database connection</h3>";
    try {
        require_once 'config.php';
        $db = Database::getInstance();
        $test_conn = $db->getConnection();
        
        $test_query = $test_conn->query("SELECT COUNT(*) as count FROM admin_users");
        if ($test_query) {
            $count = $test_query->fetch_assoc()['count'];
            echo "<div style='color: green;'>✅ Config.php database connection working - found $count admin users</div>";
        } else {
            echo "<div style='color: red;'>❌ Config.php database query failed</div>";
        }
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ Config.php error: " . $e->getMessage() . "</div>";
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Setup error: " . $e->getMessage() . "</div>";
}

echo "<hr>";
echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='admin/login.php' class='btn btn-primary' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Admin Login</a>";
echo "</div>";
?>
