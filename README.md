# Solomon Islands Scholarship Application System

A comprehensive PHP/MySQL web application for managing scholarship applications for the Papua New Guinea Higher Education Program.

## Features

### User Features
- **Multi-step Application Form**: User-friendly 5-step form with progress tracking
- **Mobile Responsive Design**: Works seamlessly on all devices
- **Real-time Validation**: Client-side and server-side validation
- **Email Confirmation**: Automatic confirmation emails with reference numbers
- **Secure Submission**: CSRF protection and data sanitization

### Admin Features
- **Secure Admin Dashboard**: Password-protected admin area
- **Application Management**: View, search, and filter applications
- **Status Updates**: Change application status with email notifications
- **Data Export**: Export applications to Excel/CSV format
- **Activity Logging**: Comprehensive logging of all activities
- **Responsive Interface**: Mobile-friendly admin dashboard

## System Requirements

- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **PHP**: 7.4 or higher (8.0+ recommended)
- **MySQL**: 5.7+ or MariaDB 10.3+
- **Extensions**: mysqli, session, mail (for email functionality)

## Installation Instructions

### 1. Download and Setup Files

1. Download all project files to your web server directory
2. Ensure your web server has read/write permissions for the project directory

### 2. Database Setup

1. Create a MySQL database:
   ```sql
   CREATE DATABASE solomon_scholarship CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. Import the database schema:
   ```bash
   mysql -u your_username -p solomon_scholarship < schema.sql
   ```

3. Or run the SQL commands from `schema.sql` in your MySQL client

### 3. Configuration

1. **Edit `config.php` file** with your actual database credentials:
   ```php
   // Database Configuration - Direct credentials
   define('DB_HOST', 'localhost');
   define('DB_USERNAME', 'u787474055_solomonislands');
   define('DB_PASSWORD', 'your_actual_password');
   define('DB_NAME', 'u787474055_solomonislands');
   ```

2. **Security**: All configuration is in plain text in `config.php` for reliability.

### 4. File Permissions

Ensure the following directories are writable by the web server:
```bash
chmod 755 assets/
chmod 755 admin/
chmod 777 logs/  # Create this directory if it doesn't exist
```

### 5. Security Configuration

1. **Change Default Admin Password**: 
   - Login with username: `admin`, password: `admin123`
   - Immediately change the password in production

2. **Update Security Settings**:
   - Change the CSRF token name in `config.php`
   - Set appropriate session timeout values
   - Configure SSL/HTTPS for production

### 6. Web Server Configuration

#### Apache (.htaccess)
Create a `.htaccess` file in the root directory:
```apache
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"

# Prevent access to sensitive files
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>
```

#### Nginx
Add to your server block:
```nginx
location ~ /config\.php$ {
    deny all;
}

location ~ /logs/ {
    deny all;
}

location ~ /admin/ {
    try_files $uri $uri/ =404;
}
```

## Usage

### For Applicants

1. **Access the Application Form**: Navigate to `index.php`
2. **Complete the Form**: Fill out all 5 steps of the application
3. **Submit**: Review and submit your application
4. **Confirmation**: Receive confirmation with reference number

### For Administrators

1. **Access Admin Panel**: Navigate to `admin/login.php`
2. **Login**: Use admin credentials
3. **View Applications**: Browse, search, and filter applications
4. **Manage Status**: Update application statuses
5. **Export Data**: Download applications as Excel/CSV

## Default Admin Credentials

**⚠️ IMPORTANT: Change these immediately in production!**

- **Username**: `admin`
- **Password**: `admin123`

## File Structure

```
solomon-scholarship/
├── index.php              # Main entry point (redirects to home.php)
├── home.php               # Landing page
├── application.php        # Multi-step application form
├── submit.php             # Form submission handler
├── thank_you.php          # Confirmation page
├── status.php             # Application status checker
├── config.php             # Configuration file (plain text)
├── schema.sql             # Database schema
├── .htaccess              # Apache security configuration
├── .gitignore             # Git ignore rules
├── README.md              # This file
├── assets/
│   ├── style.css          # Custom CSS styles
│   ├── script.js          # JavaScript functionality
│   └── README.md          # Assets documentation
├── admin/
│   ├── index.php          # Admin dashboard
│   ├── login.php          # Admin login
│   ├── logout.php         # Admin logout
│   ├── view.php           # View application details
│   ├── update_status.php  # Update application status
│   └── export.php         # Export functionality
└── logs/                  # Application logs (auto-created)
```

## Database Tables

- **applications**: Main application data
- **admin_users**: Admin user accounts
- **admin_sessions**: Admin login sessions
- **application_status_log**: Status change history

## Security Features

- **Plain Text Configuration**: Database credentials stored securely in `config.php`
- **CSRF Token Protection**: Prevents cross-site request forgery attacks
- **SQL Injection Prevention**: Uses prepared statements for all database queries
- **XSS Protection**: Input sanitization and output encoding
- **Session Management**: Secure session handling with timeouts
- **Activity Logging**: Comprehensive logging of all system activities
- **Secure Password Hashing**: Uses PHP's password_hash() function
- **File Protection**: `.htaccess` rules protect sensitive files
- **Security Headers**: HTTP security headers via `.htaccess`
- **Input Validation**: Server-side validation for all form inputs

## Customization

### Adding New Fields

1. Update the database schema in `schema.sql`
2. Add form fields in `index.php`
3. Update validation in `submit.php`
4. Modify admin views in `admin/view.php`

### Styling

- Modify `assets/style.css` for custom styling
- The system uses Bootstrap 5 for responsive design
- Font Awesome icons are included

### Email Templates

- Customize email templates in `submit.php` and `admin/update_status.php`
- Update email settings in `config.php`

## Troubleshooting

### Common Issues

1. **Database Connection Error**:
   - Check database credentials in `config.php`
   - Ensure MySQL service is running
   - Verify database exists

2. **Permission Denied**:
   - Check file permissions
   - Ensure web server can write to logs directory

3. **Email Not Sending**:
   - Configure SMTP settings in `config.php`
   - Check server mail configuration
   - Verify firewall settings

4. **Session Issues**:
   - Check PHP session configuration
   - Ensure session directory is writable

### Debug Mode

To enable debug mode, add to `config.php`:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

**Note**: Disable debug mode in production!

## Support

For technical support or questions:
- Check the application logs in the `logs/` directory
- Review error messages in the browser console
- Verify database connectivity and permissions

## License

This project is developed for the Solomon Islands Government Papua New Guinea Scholarship Program.

## Version History

- **v1.0.0**: Initial release with full functionality
  - Multi-step application form
  - Admin dashboard
  - Email notifications
  - Data export capabilities

---

**Important**: Always backup your database before making any changes or updates to the system.
