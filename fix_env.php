<?php
/**
 * Quick Fix for .env File Issue
 * Run this file to create the .env file and test the configuration
 */

echo "<!DOCTYPE html>
<html>
<head>
    <title>Solomon Islands Scholarship - Environment Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px 10px 0; }
    </style>
</head>
<body>
<div class='container'>
    <h1>🔧 Solomon Islands Scholarship - Environment Fix</h1>";

$current_dir = __DIR__;
$env_file = $current_dir . '/.env';

echo "<div class='info'><strong>Current Directory:</strong> $current_dir</div>";

// Step 1: Create .env file
$env_content = '# Solomon Islands Scholarship Application - Environment Configuration
# IMPORTANT: Never commit this file to version control!

# Database Configuration
DB_HOST=localhost
DB_USERNAME=u787474055_solomonislands
DB_PASSWORD=Blackpanther707@707
DB_NAME=u787474055_solomonislands

# Application Configuration
APP_NAME="Solomon Islands Scholarship Application"
APP_VERSION=1.0.0
APP_ENV=production
APP_DEBUG=false

# Security Configuration
CSRF_TOKEN_NAME=csrf_token
SESSION_TIMEOUT=3600

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
FROM_EMAIL=<EMAIL>
FROM_NAME="Solomon Islands Scholarship Program"
ADMIN_EMAIL=<EMAIL>

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_DIR=uploads/

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/application.log

# Admin Configuration
ADMIN_SESSION_TIMEOUT=7200
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD_HASH=$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi

# Timezone
APP_TIMEZONE=Pacific/Guadalcanal
';

// Create .env file
if (file_put_contents($env_file, $env_content)) {
    echo "<div class='success'>✅ Successfully created .env file at: $env_file</div>";
} else {
    echo "<div class='error'>❌ Failed to create .env file. Check file permissions.</div>";
}

// Step 2: Test if .env file exists and is readable
if (file_exists($env_file) && is_readable($env_file)) {
    echo "<div class='success'>✅ .env file exists and is readable</div>";
    
    // Step 3: Test environment loader
    if (file_exists('env_loader.php')) {
        try {
            require_once 'env_loader.php';
            EnvLoader::load($env_file);
            
            $db_host = EnvLoader::get('DB_HOST');
            $db_user = EnvLoader::get('DB_USERNAME');
            $db_name = EnvLoader::get('DB_NAME');
            
            echo "<div class='success'>✅ Environment loader working correctly</div>";
            echo "<div class='info'>
                <strong>Loaded Configuration:</strong><br>
                Database Host: $db_host<br>
                Database User: $db_user<br>
                Database Name: $db_name
            </div>";
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ Environment loader error: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div class='error'>❌ env_loader.php file not found</div>";
    }
    
    // Step 4: Test database connection
    try {
        $host = EnvLoader::get('DB_HOST', 'localhost');
        $username = EnvLoader::get('DB_USERNAME');
        $password = EnvLoader::get('DB_PASSWORD');
        $database = EnvLoader::get('DB_NAME');
        
        $conn = new mysqli($host, $username, $password, $database);
        
        if ($conn->connect_error) {
            echo "<div class='error'>❌ Database connection failed: " . $conn->connect_error . "</div>";
            echo "<div class='info'>Please check your database credentials in the .env file</div>";
        } else {
            echo "<div class='success'>✅ Database connection successful!</div>";
            
            // Check if tables exist
            $tables = ['applications', 'admin_users'];
            $existing_tables = [];
            
            foreach ($tables as $table) {
                $result = $conn->query("SHOW TABLES LIKE '$table'");
                if ($result && $result->num_rows > 0) {
                    $existing_tables[] = $table;
                }
            }
            
            if (count($existing_tables) > 0) {
                echo "<div class='success'>✅ Found database tables: " . implode(', ', $existing_tables) . "</div>";
            } else {
                echo "<div class='error'>❌ No application tables found. You need to import the database schema</div>";
                echo "<div class='info'>
                    <strong>Database Import Instructions:</strong><br>
                    1. Open phpMyAdmin in your hosting control panel<br>
                    2. Select your database: u787474055_solomonislands<br>
                    3. Click 'Import' tab<br>
                    4. Upload the file: <strong>schema_import.sql</strong> (not schema.sql)<br>
                    5. Click 'Go' to import<br><br>
                    <strong>Note:</strong> Use schema_import.sql instead of schema.sql for shared hosting.
                </div>";
            }
            
            $conn->close();
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Database test error: " . $e->getMessage() . "</div>";
    }
    
} else {
    echo "<div class='error'>❌ .env file not accessible</div>";
}

// Step 5: Create necessary directories
$directories = ['logs', 'uploads'];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "<div class='success'>✅ Created directory: $dir</div>";
        } else {
            echo "<div class='error'>❌ Failed to create directory: $dir</div>";
        }
    } else {
        echo "<div class='success'>✅ Directory exists: $dir</div>";
    }
}

echo "<h3>🚀 Next Steps:</h3>";
echo "<div class='info'>
    <ol>
        <li>If database connection failed, import the database schema using your hosting panel or command line</li>
        <li>Test the application: <a href='index.php' class='btn'>Open Application Form</a></li>
        <li>Test admin panel: <a href='admin/login.php' class='btn'>Open Admin Login</a></li>
        <li>Delete this fix_env.php file after everything works</li>
    </ol>
</div>";

echo "<h3>🔧 Alternative Solution:</h3>";
echo "<div class='info'>
    If you continue having issues with the .env file, you can use the simple configuration:
    <ol>
        <li>Rename 'config.php' to 'config_env.php'</li>
        <li>Rename 'config_simple.php' to 'config.php'</li>
        <li>This will use direct configuration without .env file</li>
    </ol>
</div>";

echo "</div></body></html>";
?>
